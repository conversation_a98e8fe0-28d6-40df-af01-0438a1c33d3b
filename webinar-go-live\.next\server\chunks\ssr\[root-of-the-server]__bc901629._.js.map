{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WebinarGOLIVE/webinar-go-live/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function Navigation() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <nav className=\"fixed w-full z-50 bg-white/90 backdrop-blur-md border-b border-gray-100\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n              <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path d=\"M8 5v10l7-5-7-5z\"/>\n              </svg>\n            </div>\n            <span className=\"text-xl font-bold font-display gradient-text\">\n              Webinar Go Live\n            </span>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link href=\"#features\" className=\"text-gray-600 hover:text-primary-600 transition-colors\">\n              Features\n            </Link>\n            <Link href=\"#how-it-works\" className=\"text-gray-600 hover:text-primary-600 transition-colors\">\n              How It Works\n            </Link>\n            <Link href=\"#pricing\" className=\"text-gray-600 hover:text-primary-600 transition-colors\">\n              Pricing\n            </Link>\n            <Link href=\"#\" className=\"text-gray-600 hover:text-primary-600 transition-colors\">\n              Login\n            </Link>\n            <Link href=\"#\" className=\"btn-primary text-white font-semibold py-3 px-6 rounded-lg shadow-lg\">\n              Get Started\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button \n              className=\"text-gray-600 hover:text-primary-600 transition-colors\"\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h16\"></path>\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-100\">\n              <Link href=\"#features\" className=\"block px-3 py-2 text-gray-600 hover:text-primary-600\">\n                Features\n              </Link>\n              <Link href=\"#how-it-works\" className=\"block px-3 py-2 text-gray-600 hover:text-primary-600\">\n                How It Works\n              </Link>\n              <Link href=\"#pricing\" className=\"block px-3 py-2 text-gray-600 hover:text-primary-600\">\n                Pricing\n              </Link>\n              <Link href=\"#\" className=\"block px-3 py-2 text-gray-600 hover:text-primary-600\">\n                Login\n              </Link>\n              <Link href=\"#\" className=\"block px-3 py-2 btn-primary text-white font-semibold rounded-lg text-center\">\n                Get Started\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAe,SAAQ;kDAC9D,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,8OAAC;oCAAK,WAAU;8CAA+C;;;;;;;;;;;;sCAMjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAyD;;;;;;8CAG1F,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAgB,WAAU;8CAAyD;;;;;;8CAG9F,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAyD;;;;;;8CAGzF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAyD;;;;;;8CAGlF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAsE;;;;;;;;;;;;sCAMjG,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,cAAc,CAAC;0CAE9B,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAO5E,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAuD;;;;;;0CAGxF,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAgB,WAAU;0CAAuD;;;;;;0CAG5F,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAuD;;;;;;0CAGvF,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAuD;;;;;;0CAGhF,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAA8E;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrH", "debugId": null}}]}