import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: 'swap',
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ['300', '400', '500', '600', '700', '800'],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "Webinar Go Live - Turn Any Video Into a Live Webinar Sales Machine",
  description: "Perfect for coaches and consultants. Create engaging webinar experiences that feel completely live, with scheduled comments, viewer tracking, and powerful analytics to boost your conversions.",
  keywords: "webinar software, live webinar simulation, sales webinars, coaching webinars, webinar analytics, scheduled webinars, multi-timezone webinars",
  authors: [{ name: "Webinar Go Live" }],
  creator: "Webinar Go Live",
  publisher: "Webinar Go Live",
  openGraph: {
    title: "Webinar Go Live - Turn Any Video Into a Live Webinar Sales Machine",
    description: "Perfect for coaches and consultants. Create engaging webinar experiences that feel completely live.",
    url: "https://webinargolive.com",
    siteName: "Webinar Go Live",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Webinar Go Live - Turn Any Video Into a Live Webinar Sales Machine",
    description: "Perfect for coaches and consultants. Create engaging webinar experiences that feel completely live.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${poppins.variable} font-sans antialiased`}
        suppressHydrationWarning={true}
      >
        {children}
      </body>
    </html>
  );
}
