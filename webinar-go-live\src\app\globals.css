@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, sans-serif;
  font-feature-settings: 'cv11', 'ss01';
  font-variation-settings: 'opsz' 32;
}

/* Custom button styles */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  transform: scale(1.05);
}

.btn-secondary {
  background: white;
  color: #2563eb;
  border: 2px solid #2563eb;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #eff6ff;
}

.gradient-text {
  background: linear-gradient(to right, #2563eb, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
