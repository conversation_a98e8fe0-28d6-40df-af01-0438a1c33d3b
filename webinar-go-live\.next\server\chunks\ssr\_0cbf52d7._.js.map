{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WebinarGOLIVE/webinar-go-live/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WebinarGOLIVE/webinar-go-live/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WebinarGOLIVE/webinar-go-live/src/components/HeroSection.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function HeroSection() {\n  return (\n    <section className=\"pt-20 pb-16 bg-gradient-to-br from-blue-50 via-white to-purple-50 overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Column - Content */}\n          <div className=\"space-y-8 animate-fade-in\">\n            <div className=\"space-y-4\">\n              <div className=\"inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium\">\n                <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z\" clipRule=\"evenodd\"></path>\n                </svg>\n                <span>Transform Your Sales Process</span>\n              </div>\n              \n              <h1 className=\"text-4xl md:text-6xl font-bold font-display leading-tight\">\n                Turn Any Video Into a\n                <span className=\"gradient-text\"> Live Webinar</span>\n                <br />Sales Machine\n              </h1>\n              \n              <p className=\"text-xl text-gray-600 leading-relaxed\">\n                Perfect for coaches and consultants. Create engaging webinar experiences \n                that feel completely live, with scheduled comments, real-time viewer simulation, \n                multi-timezone scheduling, and powerful analytics to boost your conversions.\n              </p>\n            </div>\n\n            {/* Stats */}\n            <div className=\"flex flex-wrap gap-8\">\n              <div className=\"flex items-center space-x-2\">\n                <svg className=\"w-5 h-5 text-primary-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                </svg>\n                <span className=\"text-gray-600\">10,000+ Active Users</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <svg className=\"w-5 h-5 text-primary-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clipRule=\"evenodd\"></path>\n                </svg>\n                <span className=\"text-gray-600\">5-Min Setup</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <svg className=\"w-5 h-5 text-primary-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z\" clipRule=\"evenodd\"></path>\n                </svg>\n                <span className=\"text-gray-600\">3x Higher Conversions</span>\n              </div>\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <Link href=\"#\" className=\"btn-primary text-white font-semibold py-3 px-6 rounded-lg shadow-lg inline-flex items-center justify-center space-x-2\">\n                <span>Start Free Trial</span>\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\"></path>\n                </svg>\n              </Link>\n              <Link href=\"#demo\" className=\"btn-secondary font-semibold py-3 px-6 rounded-lg inline-flex items-center justify-center space-x-2\">\n                <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M8 5v10l7-5-7-5z\"/>\n                </svg>\n                <span>Watch Demo</span>\n              </Link>\n            </div>\n          </div>\n\n          {/* Right Column - Visual */}\n          <div className=\"relative animate-slide-up\">\n            <div className=\"relative\">\n              {/* Main Dashboard Mockup */}\n              <div className=\"bg-white rounded-2xl shadow-2xl p-6 border border-gray-100\">\n                <div className=\"flex items-center space-x-3 mb-4\">\n                  <div className=\"w-3 h-3 bg-red-400 rounded-full\"></div>\n                  <div className=\"w-3 h-3 bg-yellow-400 rounded-full\"></div>\n                  <div className=\"w-3 h-3 bg-green-400 rounded-full\"></div>\n                </div>\n                \n                {/* Fake Video Player */}\n                <div className=\"bg-gray-900 rounded-lg aspect-video relative overflow-hidden mb-4\">\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-600 opacity-80\"></div>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm\">\n                      <svg className=\"w-8 h-8 text-white ml-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M8 5v10l7-5-7-5z\"/>\n                      </svg>\n                    </div>\n                  </div>\n                  \n                  {/* Live Badge */}\n                  <div className=\"absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1\">\n                    <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n                    <span>LIVE</span>\n                  </div>\n                  \n                  {/* Viewer Count */}\n                  <div className=\"absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm\">\n                    247 viewers\n                  </div>\n                </div>\n                \n                {/* Comments Section */}\n                <div className=\"space-y-2\">\n                  <div className=\"bg-gray-50 rounded-lg p-3\">\n                    <div className=\"flex items-start space-x-2\">\n                      <div className=\"w-6 h-6 bg-blue-500 rounded-full flex-shrink-0\"></div>\n                      <div>\n                        <p className=\"text-sm font-medium\">Sarah M.</p>\n                        <p className=\"text-sm text-gray-600\">This is exactly what I needed for my coaching business!</p>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"bg-gray-50 rounded-lg p-3\">\n                    <div className=\"flex items-start space-x-2\">\n                      <div className=\"w-6 h-6 bg-green-500 rounded-full flex-shrink-0\"></div>\n                      <div>\n                        <p className=\"text-sm font-medium\">Mike R.</p>\n                        <p className=\"text-sm text-gray-600\">Amazing results! My conversion rate doubled.</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              {/* Floating Elements */}\n              <div className=\"absolute -top-4 -right-4 bg-white rounded-lg shadow-lg p-3 border border-gray-100\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                  <span className=\"text-sm font-medium\">Live Analytics</span>\n                </div>\n              </div>\n              \n              <div className=\"absolute -bottom-4 -left-4 bg-white rounded-lg shadow-lg p-3 border border-gray-100\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-primary-600\">85%</div>\n                  <div className=\"text-xs text-gray-500\">Retention Rate</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAA2K,UAAS;;;;;;;;;;;0DAEjN,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC;wCAAG,WAAU;;4CAA4D;0DAExE,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;;;;;4CAAK;;;;;;;kDAGR,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAQvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAA2B,MAAK;gDAAe,SAAQ;0DACpE,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;0DAEV,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAA2B,MAAK;gDAAe,SAAQ;0DACpE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;0DAE3J,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAA2B,MAAK;gDAAe,SAAQ;0DACpE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAA2K,UAAS;;;;;;;;;;;0DAEjN,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;0CAKpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC;0DAAK;;;;;;0DACN,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;;kDAGzE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;;0DAC3B,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;0DAEV,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAMZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAIjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA0B,MAAK;4DAAe,SAAQ;sEACnE,cAAA,8OAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;8DAMd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;sEAAK;;;;;;;;;;;;8DAIR,8OAAC;oDAAI,WAAU;8DAAgG;;;;;;;;;;;;sDAMjH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAsB;;;;;;kFACnC,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;8DAI3C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAsB;;;;;;kFACnC,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ/C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;8CAI1C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WebinarGOLIVE/webinar-go-live/src/components/FeaturesSection.tsx"], "sourcesContent": ["export default function FeaturesSection() {\n  const features = [\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n        </svg>\n      ),\n      title: \"Time-Synchronized Playback\",\n      description: \"Viewers who join late automatically start from the current timestamp, just like real live events. Perfect simulation of live webinar experience.\",\n      color: \"bg-blue-500\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"></path>\n        </svg>\n      ),\n      title: \"Scheduled Comments & Links\",\n      description: \"Pre-schedule comments with timestamps and links to appear at specific moments. Copy-paste bulk comments in 'Name: Comment' format for easy setup.\",\n      color: \"bg-green-500\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n        </svg>\n      ),\n      title: \"Dynamic Viewer Simulation\",\n      description: \"Realistic viewer count that updates at controlled intervals. Define viewer ranges and update frequencies to create authentic live engagement.\",\n      color: \"bg-purple-500\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n        </svg>\n      ),\n      title: \"Advanced Analytics Dashboard\",\n      description: \"Track individual viewer retention, join/drop-off times, percentage watched with progress bars, and color-coded engagement segments (90%, 75%, etc.).\",\n      color: \"bg-orange-500\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n        </svg>\n      ),\n      title: \"Multi-Timezone Scheduling\",\n      description: \"Schedule webinars across different time zones with automatic conversion. Attendees can choose their preferred time slots during registration.\",\n      color: \"bg-cyan-500\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\n        </svg>\n      ),\n      title: \"Instant Embed & Forms\",\n      description: \"Generate embed codes for WordPress or any website. Create beautiful registration forms with custom fields and automatic email notifications via SendGrid.\",\n      color: \"bg-pink-500\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"></path>\n        </svg>\n      ),\n      title: \"Multi-Source Video Support\",\n      description: \"Upload videos directly or use Vimeo/Wistia links. All video controls are hidden to maintain the live experience. Supports custom duration input for external videos.\",\n      color: \"bg-indigo-500\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"></path>\n        </svg>\n      ),\n      title: \"Session Persistence & Merging\",\n      description: \"If users refresh and re-enter with same details, sessions are merged for accurate analytics. Maintains continuous tracking across multiple joins.\",\n      color: \"bg-emerald-500\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"></path>\n        </svg>\n      ),\n      title: \"Multi-Step Webinar Wizard\",\n      description: \"Intuitive wizard guides you through webinar creation: upload video, set schedule, configure comments, customize viewer settings, and generate embed codes.\",\n      color: \"bg-violet-500\"\n    }\n  ];\n\n  return (\n    <section id=\"features\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold font-display mb-4\">\n            Everything You Need to Create\n            <span className=\"gradient-text\"> Engaging Live Webinars</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Transform your sales videos into powerful webinar experiences with features\n            designed specifically for coaches and consultants who want to maximize conversions.\n          </p>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <div key={index} className=\"group p-6 bg-white rounded-xl border border-gray-100 hover:border-primary-200 card-hover\">\n              <div className=\"flex items-start space-x-4\">\n                <div className={`${feature.color} p-3 rounded-lg group-hover:scale-110 transition-transform duration-300`}>\n                  {feature.icon}\n                </div>\n                <div className=\"flex-1\">\n                  <h3 className=\"text-lg font-semibold mb-2 group-hover:text-primary-600 transition-colors\">\n                    {feature.title}\n                  </h3>\n                  <p className=\"text-gray-600 leading-relaxed\">\n                    {feature.description}\n                  </p>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAqB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC5E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAqB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC5E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAqB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC5E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAqB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC5E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAqB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC5E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAqB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC5E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAqB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC5E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAqB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC5E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAqB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC5E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAmD;8CAE/D,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAAgB,WAAU;sCACzB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,GAAG,QAAQ,KAAK,CAAC,uEAAuE,CAAC;kDACtG,QAAQ,IAAI;;;;;;kDAEf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;2BAVlB;;;;;;;;;;;;;;;;;;;;;AAoBtB", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WebinarGOLIVE/webinar-go-live/src/components/HowItWorksSection.tsx"], "sourcesContent": ["export default function HowItWorksSection() {\n  const steps = [\n    {\n      number: \"01\",\n      title: \"Upload Your Video\",\n      description: \"Upload your sales video directly or provide Vimeo/Wistia links. Our system automatically detects duration or lets you specify it manually for external videos.\",\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n        </svg>\n      ),\n      color: \"from-blue-500 to-cyan-500\"\n    },\n    {\n      number: \"02\", \n      title: \"Schedule Multiple Sessions\",\n      description: \"Set up multiple webinar dates and times across different time zones. Attendees can choose their preferred slot during registration with automatic time conversion.\",\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n        </svg>\n      ),\n      color: \"from-purple-500 to-pink-500\"\n    },\n    {\n      number: \"03\",\n      title: \"Configure Live Elements\", \n      description: \"Set up scheduled comments with timestamps and links. Configure viewer count ranges and update intervals. Enable random comment simulation for authentic engagement.\",\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n        </svg>\n      ),\n      color: \"from-green-500 to-emerald-500\"\n    },\n    {\n      number: \"04\",\n      title: \"Create Registration Forms\",\n      description: \"Build beautiful registration forms with custom fields. Choose which webinar sessions to include and customize email notifications with SendGrid integration.\",\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n        </svg>\n      ),\n      color: \"from-orange-500 to-red-500\"\n    },\n    {\n      number: \"05\",\n      title: \"Launch & Embed\",\n      description: \"Generate shareable links and embed codes for WordPress or any website. Your webinar runs automatically at scheduled times with full live simulation.\",\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\"></path>\n        </svg>\n      ),\n      color: \"from-indigo-500 to-purple-500\"\n    },\n    {\n      number: \"06\",\n      title: \"Track & Optimize\",\n      description: \"Monitor detailed analytics including individual viewer retention, engagement metrics, and conversion rates. Use insights to optimize future webinars.\",\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n        </svg>\n      ),\n      color: \"from-teal-500 to-blue-500\"\n    }\n  ];\n\n  return (\n    <section id=\"how-it-works\" className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold font-display mb-4\">\n            How It Works\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Create professional live webinar experiences in just 6 simple steps. \n            Our intuitive wizard guides you through the entire process.\n          </p>\n        </div>\n\n        {/* Steps */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {steps.map((step, index) => (\n            <div key={index} className=\"relative\">\n              {/* Connector Line */}\n              {index < steps.length - 1 && (\n                <div className=\"hidden lg:block absolute top-16 left-full w-8 h-0.5 bg-gradient-to-r from-gray-300 to-transparent transform translate-x-4\"></div>\n              )}\n              \n              <div className=\"bg-white rounded-2xl p-8 shadow-lg border border-gray-100 card-hover h-full\">\n                {/* Step Number */}\n                <div className=\"flex items-center mb-6\">\n                  <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${step.color} flex items-center justify-center text-white font-bold text-lg mr-4`}>\n                    {step.number}\n                  </div>\n                  <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${step.color} flex items-center justify-center text-white`}>\n                    {step.icon}\n                  </div>\n                </div>\n\n                {/* Content */}\n                <h3 className=\"text-xl font-bold font-display mb-4 text-gray-900\">\n                  {step.title}\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  {step.description}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-6\">\n            <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\"></path>\n            </svg>\n            <span>Setup takes less than 5 minutes</span>\n          </div>\n          <h3 className=\"text-2xl font-bold font-display mb-4\">\n            Ready to Create Your First Live Webinar?\n          </h3>\n          <p className=\"text-gray-600 mb-8 max-w-2xl mx-auto\">\n            Join thousands of coaches and consultants who are already using Webinar Go Live \n            to create engaging webinar experiences and boost their conversions.\n          </p>\n          <a href=\"#\" className=\"btn-primary text-white font-semibold py-3 px-8 rounded-lg shadow-lg inline-flex items-center space-x-2\">\n            <span>Start Your Free Trial</span>\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\"></path>\n            </svg>\n          </a>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCACjE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;kCACrE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;wBAAI,GAAE;;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAY;oBAAI,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAAgB,WAAU;;gCAExB,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;oCAAI,WAAU;;;;;;8CAGjB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,wCAAwC,EAAE,KAAK,KAAK,CAAC,mEAAmE,CAAC;8DACvI,KAAK,MAAM;;;;;;8DAEd,8OAAC;oDAAI,WAAW,CAAC,sCAAsC,EAAE,KAAK,KAAK,CAAC,4CAA4C,CAAC;8DAC9G,KAAK,IAAI;;;;;;;;;;;;sDAKd,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;;;;;;;2BAtBb;;;;;;;;;;8BA8Bd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAe,SAAQ;8CACnD,cAAA,8OAAC;wCAAK,UAAS;wCAAU,GAAE;wCAAwI,UAAS;;;;;;;;;;;8CAE9K,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAGrD,8OAAC;4BAAE,WAAU;sCAAuC;;;;;;sCAIpD,8OAAC;4BAAE,MAAK;4BAAI,WAAU;;8CACpB,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF", "debugId": null}}, {"offset": {"line": 1415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WebinarGOLIVE/webinar-go-live/src/components/PricingSection.tsx"], "sourcesContent": ["export default function PricingSection() {\n  const plans = [\n    {\n      name: \"Starter\",\n      description: \"Perfect for getting started with webinar marketing\",\n      price: \"$29\",\n      period: \"/month\",\n      popular: false,\n      features: [\n        \"5 webinars per month\",\n        \"Up to 100 viewers per webinar\",\n        \"Basic analytics & reporting\",\n        \"Email support\",\n        \"Multi-timezone scheduling\",\n        \"Video upload & external links\",\n        \"Basic comment scheduling\",\n        \"Standard embed codes\"\n      ],\n      buttonText: \"Start Free Trial\",\n      buttonStyle: \"btn-secondary\"\n    },\n    {\n      name: \"Professional\",\n      description: \"Ideal for coaches and consultants scaling their business\",\n      price: \"$79\",\n      period: \"/month\",\n      popular: true,\n      features: [\n        \"25 webinars per month\",\n        \"Up to 500 viewers per webinar\",\n        \"Advanced analytics & retention tracking\",\n        \"Priority support\",\n        \"Multi-timezone scheduling\",\n        \"Video upload & external links\",\n        \"Advanced comment scheduling with links\",\n        \"Custom registration forms\",\n        \"SendGrid email integration\",\n        \"Viewer simulation controls\",\n        \"Session merging & persistence\",\n        \"WordPress embed optimization\"\n      ],\n      buttonText: \"Start Free Trial\",\n      buttonStyle: \"btn-primary\"\n    },\n    {\n      name: \"Enterprise\",\n      description: \"For agencies and large organizations\",\n      price: \"$199\",\n      period: \"/month\",\n      popular: false,\n      features: [\n        \"Unlimited webinars\",\n        \"Unlimited viewers\",\n        \"White-label solution\",\n        \"24/7 phone support\",\n        \"Multi-timezone scheduling\",\n        \"Video upload & external links\",\n        \"Advanced comment scheduling with links\",\n        \"Custom registration forms\",\n        \"SendGrid email integration\",\n        \"Advanced viewer simulation\",\n        \"Session merging & persistence\",\n        \"Custom branding\",\n        \"API access\",\n        \"Dedicated account manager\"\n      ],\n      buttonText: \"Contact Sales\",\n      buttonStyle: \"btn-secondary\"\n    }\n  ];\n\n  return (\n    <section id=\"pricing\" className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold font-display mb-4\">\n            Simple, Transparent Pricing\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Choose the perfect plan for your business. All plans include a 14-day free trial \n            with no credit card required.\n          </p>\n        </div>\n\n        {/* Pricing Cards */}\n        <div className=\"grid md:grid-cols-3 gap-8 max-w-6xl mx-auto\">\n          {plans.map((plan, index) => (\n            <div \n              key={index} \n              className={`relative bg-white rounded-2xl shadow-lg p-8 card-hover ${\n                plan.popular \n                  ? 'border-2 border-primary-500 transform scale-105' \n                  : 'border-2 border-gray-100'\n              }`}\n            >\n              {/* Popular Badge */}\n              {plan.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                  <div className=\"bg-primary-600 text-white px-4 py-2 rounded-full text-sm font-medium\">\n                    Most Popular\n                  </div>\n                </div>\n              )}\n\n              <div className=\"text-center mb-8\">\n                {/* Plan Icon */}\n                <div className={`w-12 h-12 mx-auto mb-4 rounded-lg flex items-center justify-center ${\n                  plan.popular ? 'bg-primary-600' : 'bg-gray-100'\n                }`}>\n                  <svg className={`w-6 h-6 ${plan.popular ? 'text-white' : 'text-gray-600'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    {index === 0 && <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>}\n                    {index === 1 && <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 3l14 9-14 9V3z\"></path>}\n                    {index === 2 && <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"></path>}\n                  </svg>\n                </div>\n\n                <h3 className=\"text-2xl font-bold mb-2\">{plan.name}</h3>\n                <p className=\"text-gray-600 mb-4\">{plan.description}</p>\n\n                <div className=\"flex items-baseline justify-center\">\n                  <span className=\"text-4xl font-bold\">{plan.price}</span>\n                  <span className=\"text-gray-500 ml-2\">{plan.period}</span>\n                </div>\n              </div>\n\n              {/* Features */}\n              <ul className=\"space-y-3 mb-8\">\n                {plan.features.map((feature, featureIndex) => (\n                  <li key={featureIndex} className=\"flex items-start space-x-3\">\n                    <svg className=\"w-5 h-5 text-green-500 flex-shrink-0 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\"></path>\n                    </svg>\n                    <span className=\"text-gray-600\">{feature}</span>\n                  </li>\n                ))}\n              </ul>\n\n              {/* CTA Button */}\n              <button className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-300 ${\n                plan.buttonStyle === 'btn-primary' \n                  ? 'bg-primary-600 text-white hover:bg-primary-700 shadow-lg hover:shadow-xl' \n                  : 'bg-gray-100 text-gray-900 hover:bg-gray-200'\n              }`}>\n                {plan.buttonText}\n              </button>\n            </div>\n          ))}\n        </div>\n\n        {/* FAQ Section */}\n        <div className=\"mt-20\">\n          <h3 className=\"text-2xl font-bold font-display text-center mb-12\">\n            Frequently Asked Questions\n          </h3>\n          <div className=\"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\">\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <h4 className=\"font-semibold mb-2\">Can I change plans anytime?</h4>\n              <p className=\"text-gray-600\">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>\n            </div>\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <h4 className=\"font-semibold mb-2\">Is there a setup fee?</h4>\n              <p className=\"text-gray-600\">No setup fees. Start your free trial today and begin creating webinars immediately.</p>\n            </div>\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <h4 className=\"font-semibold mb-2\">What video formats are supported?</h4>\n              <p className=\"text-gray-600\">We support MP4, MOV, AVI uploads, plus Vimeo and Wistia links for external hosting.</p>\n            </div>\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <h4 className=\"font-semibold mb-2\">Do you offer refunds?</h4>\n              <p className=\"text-gray-600\">Yes, we offer a 30-day money-back guarantee if you're not completely satisfied.</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Final CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white rounded-2xl p-8 md:p-12 relative overflow-hidden\">\n            <div className=\"relative z-10\">\n              <h3 className=\"text-2xl md:text-3xl font-bold font-display mb-4\">\n                Ready to Transform Your Sales Process?\n              </h3>\n              <p className=\"text-white/90 mb-8 max-w-2xl mx-auto\">\n                Join thousands of coaches and consultants who are already using Webinar Go Live \n                to create engaging webinar experiences and boost their conversions.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <a href=\"#\" className=\"bg-white text-primary-600 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl inline-flex items-center justify-center space-x-2\">\n                  <span>Start Your Free Trial</span>\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\"></path>\n                  </svg>\n                </a>\n                <a href=\"#demo\" className=\"border-2 border-white text-white hover:bg-white hover:text-primary-600 font-semibold py-4 px-8 rounded-lg transition-all duration-300 inline-flex items-center justify-center space-x-2\">\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M8 5v10l7-5-7-5z\"/>\n                  </svg>\n                  <span>Watch Demo</span>\n                </a>\n              </div>\n              <div className=\"mt-6\">\n                <p className=\"text-white/80 text-sm\">\n                  ✓ 14-day free trial • ✓ No credit card required • ✓ Cancel anytime\n                </p>\n              </div>\n            </div>\n            {/* Background decoration */}\n            <div className=\"absolute top-0 left-0 w-full h-full opacity-10\">\n              <div className=\"absolute top-10 left-10 w-20 h-20 bg-white rounded-full animate-pulse-slow\"></div>\n              <div className=\"absolute top-32 right-20 w-16 h-16 bg-white rounded-full animate-pulse-slow\"></div>\n              <div className=\"absolute bottom-20 left-1/4 w-12 h-12 bg-white rounded-full animate-pulse-slow\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,QAAQ;YACR,SAAS;YACT,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,aAAa;QACf;QACA;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,QAAQ;YACR,SAAS;YACT,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,aAAa;QACf;QACA;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,QAAQ;YACR,SAAS;YACT,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAEC,WAAW,CAAC,uDAAuD,EACjE,KAAK,OAAO,GACR,oDACA,4BACJ;;gCAGD,KAAK,OAAO,kBACX,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAuE;;;;;;;;;;;8CAM1F,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAW,CAAC,mEAAmE,EAClF,KAAK,OAAO,GAAG,mBAAmB,eAClC;sDACA,cAAA,8OAAC;gDAAI,WAAW,CAAC,QAAQ,EAAE,KAAK,OAAO,GAAG,eAAe,iBAAiB;gDAAE,MAAK;gDAAO,QAAO;gDAAe,SAAQ;;oDACnH,UAAU,mBAAK,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;oDACpF,UAAU,mBAAK,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;oDACpF,UAAU,mBAAK,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;sDAIzF,8OAAC;4CAAG,WAAU;sDAA2B,KAAK,IAAI;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAsB,KAAK,WAAW;;;;;;sDAEnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAsB,KAAK,KAAK;;;;;;8DAChD,8OAAC;oDAAK,WAAU;8DAAsB,KAAK,MAAM;;;;;;;;;;;;;;;;;;8CAKrD,8OAAC;oCAAG,WAAU;8CACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,8OAAC;4CAAsB,WAAU;;8DAC/B,8OAAC;oDAAI,WAAU;oDAA8C,MAAK;oDAAe,SAAQ;8DACvF,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;8DAE3J,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;;2CAJ1B;;;;;;;;;;8CAUb,8OAAC;oCAAO,WAAW,CAAC,sEAAsE,EACxF,KAAK,WAAW,KAAK,gBACjB,6EACA,+CACJ;8CACC,KAAK,UAAU;;;;;;;2BAvDb;;;;;;;;;;8BA8DX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;8BAMnC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,8OAAC;wCAAE,WAAU;kDAAuC;;;;;;kDAIpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,MAAK;gDAAI,WAAU;;kEACpB,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAE,MAAK;gDAAQ,WAAU;;kEACxB,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;kEAEV,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAGV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAMzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}, {"offset": {"line": 2010, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WebinarGOLIVE/webinar-go-live/src/components/TestimonialsSection.tsx"], "sourcesContent": ["export default function TestimonialsSection() {\n  const testimonials = [\n    {\n      name: \"<PERSON>\",\n      role: \"Business Coach\",\n      company: \"Success Strategies\",\n      image: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80\",\n      content: \"Webinar Go Live transformed my business! My conversion rates increased by 300% within the first month. The live simulation is so realistic that attendees never suspect it's pre-recorded.\",\n      rating: 5,\n      results: \"300% increase in conversions\"\n    },\n    {\n      name: \"<PERSON>\",\n      role: \"Marketing Consultant\",\n      company: \"Growth Labs\",\n      image: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80\",\n      content: \"The analytics feature is incredible. I can see exactly when people drop off and optimize my content accordingly. The scheduled comments feature creates perfect engagement timing.\",\n      rating: 5,\n      results: \"85% average retention rate\"\n    },\n    {\n      name: \"Emily Rodriguez\",\n      role: \"Life Coach\",\n      company: \"Mindful Transformations\",\n      image: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80\",\n      content: \"I love how easy it is to schedule webinars across different time zones. My international clients can finally attend at convenient times. The setup wizard is incredibly intuitive.\",\n      rating: 5,\n      results: \"50% more international clients\"\n    },\n    {\n      name: \"David Thompson\",\n      role: \"Sales Trainer\",\n      company: \"Peak Performance\",\n      image: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80\",\n      content: \"The time-synchronized playback is genius! Late joiners automatically catch up to the current timestamp, just like a real live event. My webinars feel completely authentic.\",\n      rating: 5,\n      results: \"Doubled webinar attendance\"\n    },\n    {\n      name: \"Lisa Park\",\n      role: \"Fitness Coach\",\n      company: \"FitLife Academy\",\n      image: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=688&q=80\",\n      content: \"The form builder and SendGrid integration saved me hours of work. Automatic email reminders ensure great attendance rates. The embed feature works perfectly on my WordPress site.\",\n      rating: 5,\n      results: \"90% attendance rate\"\n    },\n    {\n      name: \"James Wilson\",\n      role: \"Business Consultant\",\n      company: \"Strategic Solutions\",\n      image: \"https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80\",\n      content: \"The viewer count simulation and random comments create such an engaging atmosphere. My clients are amazed by the 'live' interaction. ROI has been phenomenal.\",\n      rating: 5,\n      results: \"400% ROI in 3 months\"\n    }\n  ];\n\n  const stats = [\n    { number: \"10,000+\", label: \"Active Users\" },\n    { number: \"2.5x\", label: \"Average Conversion Boost\" },\n    { number: \"50M+\", label: \"Minutes Watched\" },\n    { number: \"95%\", label: \"Customer Satisfaction\" }\n  ];\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold font-display mb-4\">\n            Trusted by <span className=\"gradient-text\">10,000+ Coaches</span> & Consultants\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            See how professionals like you are transforming their sales process \n            and boosting conversions with Webinar Go Live.\n          </p>\n        </div>\n\n        {/* Stats */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mb-16\">\n          {stats.map((stat, index) => (\n            <div key={index} className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold text-primary-600 mb-2\">\n                {stat.number}\n              </div>\n              <div className=\"text-gray-600 font-medium\">\n                {stat.label}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Testimonials Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {testimonials.map((testimonial, index) => (\n            <div key={index} className=\"bg-white rounded-2xl p-6 shadow-lg border border-gray-100 card-hover\">\n              {/* Rating */}\n              <div className=\"flex items-center mb-4\">\n                {[...Array(testimonial.rating)].map((_, i) => (\n                  <svg key={i} className=\"w-5 h-5 text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n                  </svg>\n                ))}\n              </div>\n\n              {/* Content */}\n              <blockquote className=\"text-gray-700 mb-6 leading-relaxed\">\n                \"{testimonial.content}\"\n              </blockquote>\n\n              {/* Results Badge */}\n              <div className=\"inline-flex items-center space-x-2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mb-4\">\n                <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\"></path>\n                </svg>\n                <span>{testimonial.results}</span>\n              </div>\n\n              {/* Author */}\n              <div className=\"flex items-center\">\n                <img \n                  src={testimonial.image} \n                  alt={testimonial.name}\n                  className=\"w-12 h-12 rounded-full object-cover mr-4\"\n                />\n                <div>\n                  <div className=\"font-semibold text-gray-900\">{testimonial.name}</div>\n                  <div className=\"text-sm text-gray-600\">{testimonial.role}</div>\n                  <div className=\"text-sm text-primary-600 font-medium\">{testimonial.company}</div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 md:p-12\">\n            <h3 className=\"text-2xl md:text-3xl font-bold font-display mb-4\">\n              Join These Successful Coaches & Consultants\n            </h3>\n            <p className=\"text-gray-600 mb-8 max-w-2xl mx-auto\">\n              Start your free trial today and see why thousands of professionals \n              trust Webinar Go Live to transform their sales process.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a href=\"#\" className=\"btn-primary text-white font-semibold py-3 px-8 rounded-lg shadow-lg inline-flex items-center justify-center space-x-2\">\n                <span>Start Free Trial</span>\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\"></path>\n                </svg>\n              </a>\n              <a href=\"#demo\" className=\"btn-secondary font-semibold py-3 px-8 rounded-lg inline-flex items-center justify-center space-x-2\">\n                <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M8 5v10l7-5-7-5z\"/>\n                </svg>\n                <span>Watch Demo</span>\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,OAAO;YACP,SAAS;YACT,QAAQ;YACR,SAAS;QACX;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,OAAO;YACP,SAAS;YACT,QAAQ;YACR,SAAS;QACX;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,OAAO;YACP,SAAS;YACT,QAAQ;YACR,SAAS;QACX;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,OAAO;YACP,SAAS;YACT,QAAQ;YACR,SAAS;QACX;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,OAAO;YACP,SAAS;YACT,QAAQ;YACR,SAAS;QACX;QACA;YACE,MAAM;YAC<PERSON>,MAAM;YACN,SAAS;YACT,OAAO;YACP,SAAS;YACT,QAAQ;YACR,SAAS;QACX;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,QAAQ;YAAW,OAAO;QAAe;QAC3C;YAAE,QAAQ;YAAQ,OAAO;QAA2B;QACpD;YAAE,QAAQ;YAAQ,OAAO;QAAkB;QAC3C;YAAE,QAAQ;YAAO,OAAO;QAAwB;KACjD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAmD;8CACpD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;gCAAsB;;;;;;;sCAEnE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CACZ,KAAK,MAAM;;;;;;8CAEd,8OAAC;oCAAI,WAAU;8CACZ,KAAK,KAAK;;;;;;;2BALL;;;;;;;;;;8BAYd,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;4BAAgB,WAAU;;8CAEzB,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM,YAAY,MAAM;qCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC;4CAAY,WAAU;4CAA0B,MAAK;4CAAe,SAAQ;sDAC3E,cAAA,8OAAC;gDAAK,GAAE;;;;;;2CADA;;;;;;;;;;8CAOd,8OAAC;oCAAW,WAAU;;wCAAqC;wCACvD,YAAY,OAAO;wCAAC;;;;;;;8CAIxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAwI,UAAS;;;;;;;;;;;sDAE9K,8OAAC;sDAAM,YAAY,OAAO;;;;;;;;;;;;8CAI5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK,YAAY,KAAK;4CACtB,KAAK,YAAY,IAAI;4CACrB,WAAU;;;;;;sDAEZ,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAA+B,YAAY,IAAI;;;;;;8DAC9D,8OAAC;oDAAI,WAAU;8DAAyB,YAAY,IAAI;;;;;;8DACxD,8OAAC;oDAAI,WAAU;8DAAwC,YAAY,OAAO;;;;;;;;;;;;;;;;;;;2BAjCtE;;;;;;;;;;8BAyCd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAI,WAAU;;0DACpB,8OAAC;0DAAK;;;;;;0DACN,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAE,MAAK;wCAAQ,WAAU;;0DACxB,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;0DAEV,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}, {"offset": {"line": 2429, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WebinarGOLIVE/webinar-go-live/src/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function Footer() {\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '#features' },\n      { name: 'How It Works', href: '#how-it-works' },\n      { name: 'Pricing', href: '#pricing' },\n      { name: 'Demo', href: '#demo' },\n      { name: 'API Documentation', href: '#' },\n      { name: 'Integrations', href: '#' }\n    ],\n    company: [\n      { name: 'About Us', href: '#' },\n      { name: 'Blog', href: '#' },\n      { name: 'Careers', href: '#' },\n      { name: 'Contact', href: '#' },\n      { name: 'Press Kit', href: '#' },\n      { name: 'Partners', href: '#' }\n    ],\n    support: [\n      { name: 'Help Center', href: '#' },\n      { name: 'Documentation', href: '#' },\n      { name: 'Community', href: '#' },\n      { name: 'Tutorials', href: '#' },\n      { name: 'System Status', href: '#' },\n      { name: 'Contact Support', href: '#' }\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '#' },\n      { name: 'Terms of Service', href: '#' },\n      { name: 'Cookie Policy', href: '#' },\n      { name: 'GDPR', href: '#' },\n      { name: 'Security', href: '#' },\n      { name: 'Compliance', href: '#' }\n    ]\n  };\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"py-16\">\n          <div className=\"grid lg:grid-cols-5 gap-8\">\n            {/* Brand Column */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-6\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M8 5v10l7-5-7-5z\"/>\n                  </svg>\n                </div>\n                <span className=\"text-xl font-bold font-display\">Webinar Go Live</span>\n              </div>\n\n              <p className=\"text-gray-400 mb-6 leading-relaxed max-w-md\">\n                Transform your sales videos into engaging live webinar experiences. \n                Perfect for coaches and consultants who want to maximize their conversions \n                with authentic live simulation technology.\n              </p>\n\n              {/* Social Links */}\n              <div className=\"flex items-center space-x-4 mb-6\">\n                <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  <span className=\"sr-only\">Twitter</span>\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  <span className=\"sr-only\">LinkedIn</span>\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  <span className=\"sr-only\">YouTube</span>\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  <span className=\"sr-only\">Facebook</span>\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                  </svg>\n                </a>\n              </div>\n\n              {/* Newsletter Signup */}\n              <div>\n                <h4 className=\"font-semibold mb-3\">Stay Updated</h4>\n                <div className=\"flex\">\n                  <input \n                    type=\"email\" \n                    placeholder=\"Enter your email\" \n                    className=\"flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-l-lg focus:outline-none focus:border-primary-500 text-white placeholder-gray-400\"\n                  />\n                  <button className=\"bg-primary-600 hover:bg-primary-700 px-4 py-2 rounded-r-lg transition-colors\">\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\"></path>\n                    </svg>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Links Columns */}\n            <div>\n              <h3 className=\"font-semibold mb-4\">Product</h3>\n              <ul className=\"space-y-2\">\n                {footerLinks.product.map((link, index) => (\n                  <li key={index}>\n                    <Link href={link.href} className=\"text-gray-400 hover:text-white transition-colors\">\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold mb-4\">Company</h3>\n              <ul className=\"space-y-2\">\n                {footerLinks.company.map((link, index) => (\n                  <li key={index}>\n                    <Link href={link.href} className=\"text-gray-400 hover:text-white transition-colors\">\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold mb-4\">Support</h3>\n              <ul className=\"space-y-2\">\n                {footerLinks.support.map((link, index) => (\n                  <li key={index}>\n                    <Link href={link.href} className=\"text-gray-400 hover:text-white transition-colors\">\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n\n          {/* Legal Links */}\n          <div className=\"border-t border-gray-800 pt-8 mt-12\">\n            <div className=\"flex flex-col md:flex-row justify-between items-center\">\n              <div className=\"flex flex-wrap gap-6 mb-4 md:mb-0\">\n                {footerLinks.legal.map((link, index) => (\n                  <Link key={index} href={link.href} className=\"text-gray-400 hover:text-white transition-colors text-sm\">\n                    {link.name}\n                  </Link>\n                ))}\n              </div>\n              <div className=\"text-gray-400 text-sm\">\n                © 2024 Webinar Go Live. All rights reserved.\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Trust Badges */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <div className=\"flex items-center space-x-8 mb-4 md:mb-0\">\n              <div className=\"flex items-center space-x-2 text-gray-400\">\n                <svg className=\"w-5 h-5 text-green-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\" clipRule=\"evenodd\"></path>\n                </svg>\n                <span className=\"text-sm\">SSL Secured</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-gray-400\">\n                <svg className=\"w-5 h-5 text-blue-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\"></path>\n                </svg>\n                <span className=\"text-sm\">GDPR Compliant</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-gray-400\">\n                <svg className=\"w-5 h-5 text-purple-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\" clipRule=\"evenodd\"></path>\n                </svg>\n                <span className=\"text-sm\">99.9% Uptime</span>\n              </div>\n            </div>\n            <div className=\"text-gray-400 text-sm\">\n              Trusted by 10,000+ professionals worldwide\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAgB,MAAM;YAAgB;YAC9C;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAqB,MAAM;YAAI;YACvC;gBAAE,MAAM;gBAAgB,MAAM;YAAI;SACnC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAI;YAC9B;gBAAE,MAAM;gBAAQ,MAAM;YAAI;YAC1B;gBAAE,MAAM;gBAAW,MAAM;YAAI;YAC7B;gBAAE,MAAM;gBAAW,MAAM;YAAI;YAC7B;gBAAE,MAAM;gBAAa,MAAM;YAAI;YAC/B;gBAAE,MAAM;gBAAY,MAAM;YAAI;SAC/B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAI;YACjC;gBAAE,MAAM;gBAAiB,MAAM;YAAI;YACnC;gBAAE,MAAM;gBAAa,MAAM;YAAI;YAC/B;gBAAE,MAAM;gBAAa,MAAM;YAAI;YAC/B;gBAAE,MAAM;gBAAiB,MAAM;YAAI;YACnC;gBAAE,MAAM;gBAAmB,MAAM;YAAI;SACtC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAI;YACpC;gBAAE,MAAM;gBAAoB,MAAM;YAAI;YACtC;gBAAE,MAAM;gBAAiB,MAAM;YAAI;YACnC;gBAAE,MAAM;gBAAQ,MAAM;YAAI;YAC1B;gBAAE,MAAM;gBAAY,MAAM;YAAI;YAC9B;gBAAE,MAAM;gBAAc,MAAM;YAAI;SACjC;IACH;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAqB,MAAK;wDAAe,SAAQ;kEAC9D,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAK,WAAU;8DAAiC;;;;;;;;;;;;sDAGnD,8OAAC;4CAAE,WAAU;sDAA8C;;;;;;sDAO3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,MAAK;oDAAI,WAAU;;sEACpB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;sEACnD,cAAA,8OAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;;sEACpB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;sEACnD,cAAA,8OAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;;sEACpB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;sEACnD,cAAA,8OAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;;sEACpB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;sEACnD,cAAA,8OAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;sDAMd,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,8OAAC;4DAAO,WAAU;sEAChB,cAAA,8OAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACjE,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ/E,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,IAAI;;;;;;mDAFL;;;;;;;;;;;;;;;;8CASf,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,IAAI;;;;;;mDAFL;;;;;;;;;;;;;;;;8CASf,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,IAAI;;;;;;mDAFL;;;;;;;;;;;;;;;;;;;;;;sCAWjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,8OAAC,4JAAA,CAAA,UAAI;gDAAa,MAAM,KAAK,IAAI;gDAAE,WAAU;0DAC1C,KAAK,IAAI;+CADD;;;;;;;;;;kDAKf,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;8BAQ7C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAe,SAAQ;0DAClE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAyG,UAAS;;;;;;;;;;;0DAE/I,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAkiB,UAAS;;;;;;;;;;;0DAExkB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAe,SAAQ;0DACnE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAA+L,UAAS;;;;;;;;;;;0DAErO,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;0CAG9B,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}, {"offset": {"line": 3139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/WebinarGOLIVE/webinar-go-live/src/app/page.tsx"], "sourcesContent": ["import Navigation from '@/components/Navigation';\nimport HeroSection from '@/components/HeroSection';\nimport FeaturesSection from '@/components/FeaturesSection';\nimport HowItWorksSection from '@/components/HowItWorksSection';\nimport PricingSection from '@/components/PricingSection';\nimport TestimonialsSection from '@/components/TestimonialsSection';\nimport Footer from '@/components/Footer';\n\nexport default function Home() {\n  return (\n    <div className=\"font-sans antialiased\">\n      <Navigation />\n      <HeroSection />\n      <FeaturesSection />\n      <HowItWorksSection />\n      <TestimonialsSection />\n      <PricingSection />\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,UAAU;;;;;0BACX,8OAAC,iIAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,qIAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,uIAAA,CAAA,UAAiB;;;;;0BAClB,8OAAC,yIAAA,CAAA,UAAmB;;;;;0BACpB,8OAAC,oIAAA,CAAA,UAAc;;;;;0BACf,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}