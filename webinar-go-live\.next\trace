[{"name": "hot-reloader", "duration": 98, "timestamp": 1044132650161, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1751300943989, "traceId": "6dfb8555ff8fc0af"}, {"name": "setup-dev-bundler", "duration": 1070316, "timestamp": 1044132194498, "id": 2, "parentId": 1, "tags": {}, "startTime": 1751300943533, "traceId": "6dfb8555ff8fc0af"}, {"name": "run-instrumentation-hook", "duration": 67, "timestamp": 1044133355056, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751300944693, "traceId": "6dfb8555ff8fc0af"}, {"name": "start-dev-server", "duration": 2208261, "timestamp": 1044131207663, "id": 1, "tags": {"cpus": "4", "platform": "win32", "memory.freeMem": "1982779392", "memory.totalMem": "8379183104", "memory.heapSizeLimit": "4390387712", "memory.rss": "190734336", "memory.heapTotal": "133095424", "memory.heapUsed": "87777128"}, "startTime": 1751300942546, "traceId": "6dfb8555ff8fc0af"}, {"name": "compile-path", "duration": 7640220, "timestamp": 1044143784732, "id": 7, "tags": {"trigger": "/"}, "startTime": 1751300955123, "traceId": "6dfb8555ff8fc0af"}, {"name": "ensure-page", "duration": 7644626, "timestamp": 1044143783701, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1751300955122, "traceId": "6dfb8555ff8fc0af"}]